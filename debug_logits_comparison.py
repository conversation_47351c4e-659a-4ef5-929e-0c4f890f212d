#!/usr/bin/env python3
"""
Debug script to compare the actual logits values between padded and unpadded approaches.
"""

import os
import sys
import torch
from pathlib import Path
from transformers import AutoTokenizer
from omegaconf import OmegaConf

# Add src directory to path
sys.path.append(os.path.dirname(os.path.realpath(__file__)))
from src.bert_layers.configuration_bert import FlexBertConfig
from src.bert_layers.model import FlexBertForMaskedLM

# Configuration
CONFIG_PATH = "yamls/main/flex-bert-modernbert-base-edu-fw-320B-paper-custom-tokenizer.yaml"
CHECKPOINT_PATH = "/s2_nfs/ckpt/flex-bert-modernbert-base-edu-fw-320B-article-custom-tokenizer/ep3-ba339282-rank0.pt"
INPUT_TEXT = "The capital of France is <mask>."
DEVICE = "cuda" if torch.cuda.is_available() else "cpu"

def load_config(config_path: str):
    """Load configuration."""
    config_path = Path(config_path)
    with open(config_path) as f:
        config = OmegaConf.load(f)
    
    defaults_path = config_path.parent.parent / "defaults.yaml"
    if defaults_path.exists():
        with open(defaults_path) as f:
            default_config = OmegaConf.load(f)
        config = OmegaConf.merge(default_config, config)
    
    OmegaConf.resolve(config)
    return OmegaConf.to_container(config, resolve=True)

def load_model_and_tokenizer():
    """Load model and tokenizer."""
    # Load config and tokenizer
    config = load_config(CONFIG_PATH)
    tokenizer_name = config.get("tokenizer_name", "bert-base-uncased")
    
    try:
        tokenizer = AutoTokenizer.from_pretrained(
            tokenizer_name,
            use_fast=True,
            trust_remote_code=True
        )
    except:
        tokenizer = AutoTokenizer.from_pretrained("bert-base-uncased")
    
    # Create model config
    model_config = config["model"]["model_config"]
    config_dict = dict(model_config)
    config_dict['vocab_size'] = len(tokenizer)
    bert_config = FlexBertConfig(**config_dict)
    
    # Create and load model
    model = FlexBertForMaskedLM(bert_config)
    checkpoint = torch.load(CHECKPOINT_PATH, map_location="cpu")
    
    if "state" in checkpoint and "model" in checkpoint["state"]:
        state_dict = checkpoint["state"]["model"]
    else:
        state_dict = checkpoint
    
    # Remove model. prefix if present
    if any(key.startswith("model.") for key in state_dict.keys()):
        new_state_dict = {}
        for key, value in state_dict.items():
            if key.startswith("model."):
                new_key = key[6:]
                new_state_dict[new_key] = value
            else:
                new_state_dict[key] = value
        state_dict = new_state_dict
    
    model.load_state_dict(state_dict)
    model = model.to(DEVICE)
    model.eval()
    
    return model, tokenizer, bert_config

def debug_logits_comparison():
    """Compare logits between padded and unpadded approaches."""
    print("🔍 LOGITS COMPARISON DEBUG")
    print("=" * 60)
    
    model, tokenizer, bert_config = load_model_and_tokenizer()
    
    print(f"Model config - padding: {bert_config.padding}")
    print(f"Model config - unpad_embeddings: {bert_config.unpad_embeddings}")
    print(f"Model config - pad_logits: {bert_config.pad_logits}")
    
    # Method 1: Padded approach (like our simple script)
    print("\n" + "=" * 40)
    print("METHOD 1: PADDED APPROACH")
    print("=" * 40)
    
    inputs_padded = tokenizer(INPUT_TEXT, return_tensors="pt", padding=True, truncation=True)
    inputs_padded = {k: v.to(DEVICE) for k, v in inputs_padded.items()}
    
    print(f"Input shape: {inputs_padded['input_ids'].shape}")
    
    with torch.no_grad():
        outputs_padded = model(**inputs_padded)
        logits_padded = outputs_padded.logits
    
    print(f"Output logits shape: {logits_padded.shape}")
    
    # Find mask position and extract logits
    mask_token_id = tokenizer.mask_token_id
    mask_positions = (inputs_padded["input_ids"] == mask_token_id).nonzero(as_tuple=True)
    
    if len(mask_positions[0]) > 0:
        batch_idx, seq_pos = mask_positions[0][0], mask_positions[1][0]
        mask_logits_padded = logits_padded[batch_idx, seq_pos]
        
        print(f"Mask position: ({batch_idx}, {seq_pos})")
        print(f"Mask logits shape: {mask_logits_padded.shape}")
        
        # Get top 5 predictions
        top_k_logits, top_k_indices = torch.topk(mask_logits_padded, 5)
        top_k_probs = torch.softmax(top_k_logits, dim=-1)
        
        print("Top 5 predictions (padded):")
        for i in range(5):
            token_id = top_k_indices[i].item()
            token = tokenizer.decode([token_id])
            prob = top_k_probs[i].item()
            print(f"  {i+1}. '{token}' (ID: {token_id}, prob: {prob:.3f})")
    
    # Method 2: Unpadded approach (like inference directory)
    print("\n" + "=" * 40)
    print("METHOD 2: UNPADDED APPROACH")
    print("=" * 40)
    
    from src.bert_padding import unpad_input
    
    # Prepare unpadded inputs
    unpadded_ids, indices, cu_seqlens, max_seqlen = unpad_input(
        inputs_padded["input_ids"].unsqueeze(-1),
        inputs_padded["attention_mask"]
    )
    unpadded_ids = unpadded_ids.squeeze(-1)
    
    inputs_unpadded = {
        "input_ids": unpadded_ids,
        "cu_seqlens": cu_seqlens,
        "max_seqlen": max_seqlen
    }
    
    print(f"Unpadded input shape: {unpadded_ids.shape}")
    
    with torch.no_grad():
        outputs_unpadded = model(**inputs_unpadded)
        logits_unpadded = outputs_unpadded.logits
    
    print(f"Unpadded output logits shape: {logits_unpadded.shape}")
    
    # Find mask position in unpadded format
    mask_positions_unpadded = (unpadded_ids == mask_token_id).nonzero(as_tuple=False).flatten()
    
    if len(mask_positions_unpadded) > 0:
        mask_pos_unpadded = mask_positions_unpadded[0].item()
        mask_logits_unpadded = logits_unpadded[mask_pos_unpadded]
        
        print(f"Mask position (unpadded): {mask_pos_unpadded}")
        print(f"Mask logits shape: {mask_logits_unpadded.shape}")
        
        # Get top 5 predictions
        top_k_logits, top_k_indices = torch.topk(mask_logits_unpadded, 5)
        top_k_probs = torch.softmax(top_k_logits, dim=-1)
        
        print("Top 5 predictions (unpadded):")
        for i in range(5):
            token_id = top_k_indices[i].item()
            token = tokenizer.decode([token_id])
            prob = top_k_probs[i].item()
            print(f"  {i+1}. '{token}' (ID: {token_id}, prob: {prob:.3f})")
    
    # Compare the logits directly
    print("\n" + "=" * 60)
    print("LOGITS COMPARISON")
    print("=" * 60)
    
    if len(mask_positions[0]) > 0 and len(mask_positions_unpadded) > 0:
        # Compare the actual logits values
        logits_diff = torch.abs(mask_logits_padded - mask_logits_unpadded).max()
        print(f"Max difference between logits: {logits_diff:.6f}")
        
        if logits_diff < 1e-5:
            print("✅ Logits are essentially identical!")
        else:
            print("❌ Logits are different!")
            
            # Show some specific differences
            print("\nSample logits comparison:")
            for i in range(min(10, len(mask_logits_padded))):
                diff = abs(mask_logits_padded[i] - mask_logits_unpadded[i])
                print(f"  Token {i}: padded={mask_logits_padded[i]:.3f}, unpadded={mask_logits_unpadded[i]:.3f}, diff={diff:.6f}")

if __name__ == "__main__":
    debug_logits_comparison()
