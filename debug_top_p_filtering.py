#!/usr/bin/env python3
"""
Debug script to test if top-p filtering is causing the punctuation issue.
"""

import torch
import torch.nn.functional as F

def test_top_p_filtering():
    """Test the top-p filtering logic from the inference directory."""
    print("🔍 TOP-P FILTERING DEBUG")
    print("=" * 60)
    
    # Simulate the logits we get from our working model
    # These are the actual logits for the top tokens we observed
    vocab_size = 50368
    
    # Create a mock logits tensor with the pattern we observed
    mask_logits = torch.zeros(vocab_size)
    
    # Set the logits for the tokens we know should be top predictions
    # Based on our working results:
    # 1. '.' (ID: 260, prob: 0.725)
    # 2. 'Orléans' (ID: 47304, prob: 0.119)  
    # 3. ',' (ID: 261, prob: 0.083)
    # 4. '?' (ID: 351, prob: 0.051)
    # 5. 'Alsace' (ID: 48502, prob: 0.022)
    
    # Convert probabilities back to logits (approximately)
    mask_logits[260] = 1.0    # '.'
    mask_logits[47304] = -1.8  # 'Orléans' 
    mask_logits[261] = -2.4   # ','
    mask_logits[351] = -2.9   # '?'
    mask_logits[48502] = -3.8  # 'Alsace'
    
    # Add some noise to other positions
    torch.manual_seed(42)
    mask_logits += torch.randn(vocab_size) * 0.1
    
    # Ensure our target tokens are still on top
    mask_logits[260] = 1.0
    mask_logits[47304] = -1.8
    mask_logits[261] = -2.4
    mask_logits[351] = -2.9
    mask_logits[48502] = -3.8
    
    print("Original top 10 tokens (before filtering):")
    top_logits_orig, top_indices_orig = torch.topk(mask_logits, 10)
    probs_orig = torch.softmax(top_logits_orig, dim=-1)
    for i in range(10):
        print(f"  {i+1}. Token {top_indices_orig[i].item()}: logit={top_logits_orig[i]:.3f}, prob={probs_orig[i]:.3f}")
    
    # Now apply the inference directory's filtering logic
    print("\n" + "=" * 40)
    print("APPLYING INFERENCE DIRECTORY FILTERING")
    print("=" * 40)
    
    top_k = 10
    top_p = 0.9  # Default from inference config
    temperature = 1.0
    
    # Step 1: Apply top-k filtering
    if top_k > 0:
        top_k_logits, top_k_indices = torch.topk(mask_logits, min(top_k, mask_logits.size(-1)))
    else:
        top_k_logits, top_k_indices = mask_logits, torch.arange(mask_logits.size(-1))
    
    print(f"After top-k filtering (k={top_k}):")
    for i in range(len(top_k_logits)):
        print(f"  {i+1}. Token {top_k_indices[i].item()}: logit={top_k_logits[i]:.3f}")
    
    # Step 2: Apply top-p filtering
    if top_p < 1.0:
        print(f"\nApplying top-p filtering (p={top_p})...")
        
        sorted_logits, sorted_indices = torch.sort(top_k_logits, descending=True)
        cumulative_probs = torch.cumsum(F.softmax(sorted_logits, dim=-1), dim=-1)
        
        print("Cumulative probabilities:")
        for i in range(len(cumulative_probs)):
            print(f"  Position {i}: cumulative_prob={cumulative_probs[i]:.3f}")
        
        # Remove tokens with cumulative probability above threshold
        sorted_indices_to_remove = cumulative_probs > top_p
        sorted_indices_to_remove[1:] = sorted_indices_to_remove[:-1].clone()
        sorted_indices_to_remove[0] = 0
        
        print(f"Tokens to remove: {sorted_indices_to_remove}")
        
        indices_to_remove = sorted_indices[sorted_indices_to_remove]
        print(f"Indices to remove: {indices_to_remove}")
        
        # Set removed tokens to -inf
        top_k_logits_filtered = top_k_logits.clone()
        top_k_logits_filtered[indices_to_remove] = -float('inf')
        
        print("After top-p filtering:")
        for i in range(len(top_k_logits_filtered)):
            if top_k_logits_filtered[i] != -float('inf'):
                print(f"  Token {top_k_indices[i].item()}: logit={top_k_logits_filtered[i]:.3f}")
    else:
        top_k_logits_filtered = top_k_logits
    
    # Step 3: Convert to probabilities and get final predictions
    probs = F.softmax(top_k_logits_filtered, dim=-1)
    
    # Get top predictions
    top_probs, top_indices_local = torch.topk(probs, min(top_k, probs.size(-1)))
    token_ids = top_k_indices[top_indices_local]
    
    print(f"\nFinal predictions after filtering:")
    for i in range(min(5, len(token_ids))):
        if top_probs[i] > 0:  # Only show non-zero probabilities
            print(f"  {i+1}. Token {token_ids[i].item()}: prob={top_probs[i]:.3f}")
    
    # Test with different top_p values
    print("\n" + "=" * 60)
    print("TESTING DIFFERENT TOP_P VALUES")
    print("=" * 60)
    
    for test_top_p in [0.5, 0.7, 0.9, 0.95, 1.0]:
        print(f"\nTesting top_p = {test_top_p}:")
        
        # Apply filtering with this top_p
        top_k_logits_test = top_k_logits.clone()
        
        if test_top_p < 1.0:
            sorted_logits, sorted_indices = torch.sort(top_k_logits_test, descending=True)
            cumulative_probs = torch.cumsum(F.softmax(sorted_logits, dim=-1), dim=-1)
            
            sorted_indices_to_remove = cumulative_probs > test_top_p
            sorted_indices_to_remove[1:] = sorted_indices_to_remove[:-1].clone()
            sorted_indices_to_remove[0] = 0
            
            indices_to_remove = sorted_indices[sorted_indices_to_remove]
            top_k_logits_test[indices_to_remove] = -float('inf')
        
        probs_test = F.softmax(top_k_logits_test, dim=-1)
        top_probs_test, top_indices_local_test = torch.topk(probs_test, min(5, probs_test.size(-1)))
        token_ids_test = top_k_indices[top_indices_local_test]
        
        for i in range(min(3, len(token_ids_test))):
            if top_probs_test[i] > 0:
                print(f"    {i+1}. Token {token_ids_test[i].item()}: prob={top_probs_test[i]:.3f}")

if __name__ == "__main__":
    test_top_p_filtering()
