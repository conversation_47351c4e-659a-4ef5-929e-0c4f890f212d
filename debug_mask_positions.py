#!/usr/bin/env python3
"""
Debug script to compare mask position detection between padded and unpadded approaches.
"""

import os
import sys
import torch
from pathlib import Path
from transformers import AutoTokenizer

# Add src directory to path
sys.path.append(os.path.dirname(os.path.realpath(__file__)))

# Import inference directory components
sys.path.insert(0, str(Path.cwd()))
import os
os.chdir('/home/<USER>/notebooks/ModernBert_pretrain')
sys.path.insert(0, '/home/<USER>/notebooks/ModernBert_pretrain')

# Removed unused imports

# Configuration
CONFIG_PATH = "yamls/main/flex-bert-modernbert-base-edu-fw-320B-paper-custom-tokenizer.yaml"
INPUT_TEXT = "The capital of France is <mask>."

def debug_mask_positions():
    """Compare mask position detection between approaches."""
    print("🔍 MASK POSITION DEBUG")
    print("=" * 60)
    
    # Load tokenizer
    tokenizer = AutoTokenizer.from_pretrained(
        "/s2_nfs/tokenizer_spm_50368_140B_EnFa_hf_edited",
        use_fast=True,
        trust_remote_code=True
    )
    
    print(f"Input text: {INPUT_TEXT}")
    print(f"Mask token: {tokenizer.mask_token}")
    print(f"Mask token ID: {tokenizer.mask_token_id}")
    
    # Method 1: Our simple approach (padded)
    print("\n" + "=" * 40)
    print("METHOD 1: SIMPLE APPROACH (PADDED)")
    print("=" * 40)
    
    inputs_padded = tokenizer(INPUT_TEXT, return_tensors="pt", padding=True, truncation=True)
    input_ids_padded = inputs_padded["input_ids"]
    attention_mask_padded = inputs_padded["attention_mask"]
    
    print(f"Padded input IDs: {input_ids_padded}")
    print(f"Padded input IDs shape: {input_ids_padded.shape}")
    print(f"Attention mask: {attention_mask_padded}")
    
    # Find mask positions in padded format
    mask_token_id = tokenizer.mask_token_id
    mask_positions_padded = (input_ids_padded == mask_token_id).nonzero(as_tuple=True)
    print(f"Mask positions (padded): {list(zip(mask_positions_padded[0].tolist(), mask_positions_padded[1].tolist()))}")
    
    # Decode tokens around mask
    for i, token_id in enumerate(input_ids_padded[0]):
        token = tokenizer.decode([token_id])
        marker = " <-- MASK" if token_id == mask_token_id else ""
        print(f"  Position {i}: {token_id} -> '{token}'{marker}")
    
    # Method 2: Manual unpadding (simulating inference directory)
    print("\n" + "=" * 40)
    print("METHOD 2: MANUAL UNPADDING (SIMULATING INFERENCE)")
    print("=" * 40)

    from src.bert_padding import unpad_input

    # Manually unpad the padded inputs (like inference directory does)
    unpadded_ids, indices, cu_seqlens, max_seqlen = unpad_input(
        input_ids_padded.unsqueeze(-1),
        attention_mask_padded
    )
    unpadded_ids = unpadded_ids.squeeze(-1)

    print(f"Unpadded input IDs: {unpadded_ids}")
    print(f"Unpadded input IDs shape: {unpadded_ids.shape}")
    print(f"Indices: {indices}")
    print(f"cu_seqlens: {cu_seqlens}")
    print(f"max_seqlen: {max_seqlen}")

    # Find mask positions in unpadded format
    mask_positions_unpadded = (unpadded_ids == mask_token_id).nonzero(as_tuple=False).flatten()
    print(f"Mask positions (unpadded): {mask_positions_unpadded.tolist()}")

    # Decode tokens in unpadded format
    for i, token_id in enumerate(unpadded_ids):
        token = tokenizer.decode([token_id])
        marker = " <-- MASK" if token_id == mask_token_id else ""
        print(f"  Position {i}: {token_id} -> '{token}'{marker}")
    
    # Method 3: Manual unpadding to verify
    print("\n" + "=" * 40)
    print("METHOD 3: MANUAL UNPADDING VERIFICATION")
    print("=" * 40)
    
    from src.bert_padding import unpad_input
    
    # Manually unpad the padded inputs
    unpadded_ids_manual, indices, cu_seqlens_manual, max_seqlen_manual = unpad_input(
        input_ids_padded.unsqueeze(-1),
        attention_mask_padded
    )
    unpadded_ids_manual = unpadded_ids_manual.squeeze(-1)
    
    print(f"Manual unpadded input IDs: {unpadded_ids_manual}")
    print(f"Manual unpadded shape: {unpadded_ids_manual.shape}")
    print(f"Manual indices: {indices}")
    print(f"Manual cu_seqlens: {cu_seqlens_manual}")
    print(f"Manual max_seqlen: {max_seqlen_manual}")
    
    # Find mask positions in manually unpadded format
    mask_positions_manual = (unpadded_ids_manual == mask_token_id).nonzero(as_tuple=False).flatten()
    print(f"Mask positions (manual unpadded): {mask_positions_manual.tolist()}")
    
    # Compare all methods
    print("\n" + "=" * 60)
    print("COMPARISON")
    print("=" * 60)
    
    print(f"Padded mask position: {mask_positions_padded[1].item() if len(mask_positions_padded[0]) > 0 else 'None'}")
    print(f"Unpadded mask position: {mask_positions_unpadded[0].item() if len(mask_positions_unpadded) > 0 else 'None'}")
    print(f"Manual unpadded mask position: {mask_positions_manual[0].item() if len(mask_positions_manual) > 0 else 'None'}")
    
    # Check if they match
    if len(mask_positions_padded[0]) > 0 and len(mask_positions_unpadded) > 0 and len(mask_positions_manual) > 0:
        unpadded_pos = mask_positions_unpadded[0].item()
        manual_pos = mask_positions_manual[0].item()
        
        if unpadded_pos == manual_pos:
            print("✅ Unpadded positions match!")
        else:
            print("❌ Unpadded positions don't match!")
            
        # Check if the token at that position is actually the mask token
        if unpadded_ids[unpadded_pos] == mask_token_id:
            print("✅ Token at unpadded position is mask token")
        else:
            print(f"❌ Token at unpadded position is {unpadded_ids[unpadded_pos]}, not mask token {mask_token_id}")

if __name__ == "__main__":
    debug_mask_positions()
